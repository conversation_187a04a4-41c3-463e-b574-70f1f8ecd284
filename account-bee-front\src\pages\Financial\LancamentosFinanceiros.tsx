import React, { useState, useEffect, useMemo } from 'react';
import { Plus, Check, Calendar, AlertTriangle, Filter, RefreshCw } from 'lucide-react';
import { Layout } from '@/components/layout';
import { Button } from '@/components/ui/forms';
import { Badge } from '@/components/ui/shadcn/badge';
import { Checkbox } from '@/components/ui/forms';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/shadcn/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/shadcn/table';
import { TablePagination } from '@/components/ui/navigation/TablePagination/TablePagination';
import { useLancamentosFinanceiros, FILTRO_TIPOS } from '@/hooks/useLancamentosFinanceiros';
import { useToast } from '@/hooks/use-toast';

export default function LancamentosFinanceiros() {
  const {
    loading,
    error,
    listaLancamentos,
    filtroAtivo,
    dadosCarregados,
    listarContasApagar,
    listarContasAreceber,
    listarExtrato,
    recarregarDados
  } = useLancamentosFinanceiros();

  const { toast } = useToast();

  const [activeTab, setActiveTab] = useState<'pagar' | 'receber'>('pagar');
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Carrega os dados automaticamente na primeira renderização
  useEffect(() => {
    if (!dadosCarregados) {
      handleTabChange('pagar');
    }
  }, [dadosCarregados]);

  // Função para lidar com mudança de aba (baseada no legacy)
  const handleTabChange = async (tab: 'pagar' | 'receber') => {
    setActiveTab(tab);
    setSelectedIds([]);
    setCurrentPage(1);

    try {
      if (tab === 'pagar') {
        await listarContasApagar();
      } else {
        await listarContasAreceber();
      }
    } catch (err) {
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar os lançamentos.',
        variant: 'destructive',
      });
    }
  };

  // Função para recarregar dados
  const handleRecarregar = async () => {
    try {
      await recarregarDados();
      // Reaplica o filtro atual
      if (activeTab === 'pagar') {
        await listarContasApagar();
      } else {
        await listarContasAreceber();
      }
      
      toast({
        title: 'Dados atualizados',
        description: 'Os lançamentos foram recarregados com sucesso.',
      });
    } catch (err) {
      toast({
        title: 'Erro',
        description: 'Não foi possível recarregar os dados.',
        variant: 'destructive',
      });
    }
  };

  // Paginação
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return listaLancamentos.slice(startIndex, endIndex);
  }, [listaLancamentos, currentPage, pageSize]);

  const totalPages = Math.ceil(listaLancamentos.length / pageSize);

  // Funções utilitárias
  const formatCurrency = (value: number) => {
    return value.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('pt-BR', { 
      day: '2-digit', 
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getStatusBadge = (lancamento: any) => {
    if (lancamento.efetivado) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800 text-xs px-2 py-0">
          <Check className="w-3 h-3 mr-1" />
          {activeTab === 'pagar' ? 'Pago' : 'Recebido'}
        </Badge>
      );
    }

    // Verifica se está vencido
    const hoje = new Date();
    const dataVencimento = new Date(lancamento.dataLancamento || lancamento.dataCompetencia);
    
    if (dataVencimento < hoje) {
      return (
        <Badge variant="destructive" className="text-xs px-2 py-0">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Vencido
        </Badge>
      );
    }

    return (
      <Badge variant="secondary" className="text-xs px-2 py-0">
        <Calendar className="w-3 h-3 mr-1" />
        Em Aberto
      </Badge>
    );
  };

  // Cálculo de totais
  const totals = useMemo(() => {
    const efetivados = listaLancamentos.filter(l => l.efetivado);
    const abertos = listaLancamentos.filter(l => !l.efetivado);
    const selecionados = listaLancamentos.filter(l => selectedIds.includes(l.id.toString()));

    return {
      efetivados: efetivados.reduce((sum, l) => sum + l.valor, 0),
      abertos: abertos.reduce((sum, l) => sum + l.valor, 0),
      selecionados: selecionados.reduce((sum, l) => sum + l.valor, 0),
      total: listaLancamentos.reduce((sum, l) => sum + l.valor, 0),
    };
  }, [listaLancamentos, selectedIds]);

  const handleSelectItem = (id: string) => {
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(i => i !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedIds.length === paginatedData.length && paginatedData.length > 0) {
      setSelectedIds([]);
    } else {
      setSelectedIds(paginatedData.map(item => item.id.toString()));
    }
  };

  return (
    <Layout>
      <div className="h-screen flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold">Lançamentos Financeiros</h1>
            {filtroAtivo && (
              <Badge variant="outline" className="text-sm">
                {filtroAtivo === FILTRO_TIPOS.CONTAS_A_PAGAR ? 'Contas a Pagar' : 
                 filtroAtivo === FILTRO_TIPOS.CONTAS_A_RECEBER ? 'Contas a Receber' : 
                 'Extrato'}
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRecarregar}
              disabled={loading}
              className="gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Recarregar
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="px-6 py-3 border-b">
          <Tabs value={activeTab} onValueChange={(value) => handleTabChange(value as 'pagar' | 'receber')}>
            <div className="flex items-center justify-between">
              <TabsList className="grid w-auto grid-cols-2">
                <TabsTrigger value="pagar" className="text-sm">
                  Pagar
                </TabsTrigger>
                <TabsTrigger value="receber" className="text-sm">
                  Receber
                </TabsTrigger>
              </TabsList>
              
              <div className="flex gap-2">
                <Button
                  size="sm"
                  className="gap-2"
                  disabled={loading}
                >
                  <Plus className="w-4 h-4" />
                  Novo {activeTab === 'pagar' ? 'Pagamento' : 'Recebimento'}
                </Button>
              </div>
            </div>
          </Tabs>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2" />
              <p>Carregando lançamentos...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-red-600">
              <AlertTriangle className="w-8 h-8 mx-auto mb-2" />
              <p>{error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRecarregar}
                className="mt-2"
              >
                Tentar novamente
              </Button>
            </div>
          </div>
        )}

        {/* Table Container */}
        {!loading && !error && (
          <div className="flex-1 flex flex-col px-6 py-2 overflow-hidden">
            <div className="rounded-md border flex-1 overflow-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-background">
                  <TableRow className="h-10">
                    <TableHead className="w-10">
                      <Checkbox 
                        checked={selectedIds.length === paginatedData.length && paginatedData.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead className="text-xs">Data</TableHead>
                    <TableHead className="text-xs">Descrição</TableHead>
                    <TableHead className="text-xs">{activeTab === 'pagar' ? 'Fornecedor' : 'Cliente'}</TableHead>
                    <TableHead className="text-xs">Local</TableHead>
                    <TableHead className="text-xs">Conta</TableHead>
                    <TableHead className="text-xs">Categoria</TableHead>
                    <TableHead className="text-xs text-right">Valor</TableHead>
                    <TableHead className="text-xs">Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedData.map((lancamento) => (
                    <TableRow key={lancamento.id} className="h-12">
                      <TableCell>
                        <Checkbox 
                          checked={selectedIds.includes(lancamento.id.toString())}
                          onCheckedChange={() => handleSelectItem(lancamento.id.toString())}
                        />
                      </TableCell>
                      <TableCell className="text-sm">
                        {formatDate(lancamento.dataLancamento || lancamento.dataCompetencia || new Date())}
                      </TableCell>
                      <TableCell className="text-sm max-w-[200px] truncate" title={lancamento.descricao}>
                        {lancamento.descricao}
                      </TableCell>
                      <TableCell className="text-sm">
                        {lancamento.fornecedorNome || lancamento.pessoaNome || '-'}
                      </TableCell>
                      <TableCell className="text-sm">{lancamento.localNome || '-'}</TableCell>
                      <TableCell className="text-sm">{lancamento.contaNome || '-'}</TableCell>
                      <TableCell className="text-sm">{lancamento.categoriaNome || '-'}</TableCell>
                      <TableCell className="text-sm text-right font-mono">
                        {formatCurrency(lancamento.valor)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(lancamento)}
                      </TableCell>
                    </TableRow>
                  ))}
                  
                  {/* Totals row */}
                  <TableRow>
                    <TableCell colSpan={9} className="border-t-2 bg-slate-50 p-4">
                      <div className="flex flex-wrap items-center justify-between gap-x-6 gap-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <span className="text-slate-600">Selecionados:</span>
                          <span className="font-mono font-semibold text-slate-900">{formatCurrency(totals.selecionados)}</span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <span className="text-slate-600">{activeTab === 'pagar' ? 'Pagos' : 'Recebidos'}:</span>
                          <span className="font-mono font-semibold text-green-600">{formatCurrency(totals.efetivados)}</span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <span className="text-slate-600">Em Aberto:</span>
                          <span className="font-mono font-semibold text-blue-600">{formatCurrency(totals.abertos)}</span>
                        </div>
                        
                        <div className="flex items-center gap-2 border-l border-slate-300 pl-4 ml-2">
                          <span className="text-slate-800 font-semibold">Total:</span>
                          <span className="font-mono font-bold text-slate-900">{formatCurrency(totals.total)}</span>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-background border border-t-0 rounded-b-lg px-4 py-3 mt-0">
                <TablePagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  pageSize={pageSize}
                  totalItems={listaLancamentos.length}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setPageSize}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </Layout>
  );
}
