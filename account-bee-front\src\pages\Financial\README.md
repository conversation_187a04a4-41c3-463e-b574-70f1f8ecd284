# Lançamentos Financeiros - Nova Implementação

Esta implementação replica o comportamento do **account-bee-legacy** para listagem de lançamentos financeiros, seguindo as regras especificadas:

## 🎯 Objetivos Alcançados

✅ **Uma única consulta**: Todos os dados são buscados em uma única chamada  
✅ **Cache local**: Dados mantidos em memória após a primeira consulta  
✅ **Filtros locais**: Alternância entre "Pagar" e "Receber" sem novas consultas  
✅ **Arquitetura limpa**: Separação de responsabilidades e reuso de código  

## 🏗️ Arquitetura

### Hook Principal: `useLancamentosFinanceiros`

```typescript
// Localização: src/hooks/useLancamentosFinanceiros.ts

const {
  // Estados
  loading,
  error,
  listaLancamentos,           // Dados filtrados
  listaLancamentosOriginal,   // Dados originais (cache)
  filtroAtivo,
  dadosCarregados,
  
  // Funções principais (baseadas no legacy)
  carregarTodosLancamentos,   // Consulta inicial
  listarContasApagar,         // Filtra despesas
  listarContasAreceber,       // Filtra receitas
  listarExtrato,              // Mostra todos
  filtrarLancamentosPorTipo,  // Filtro genérico
  recarregarDados,            // Força nova consulta
} = useLancamentosFinanceiros();
```

### Componentes

1. **LancamentosFinanceiros.tsx** - Tela principal completa
2. **LancamentosDemo.tsx** - Demonstração do funcionamento
3. **README.md** - Esta documentação

## 🚀 Como Usar

### Exemplo Básico

```typescript
import { useLancamentosFinanceiros } from '@/hooks/useLancamentosFinanceiros';

function MinhaTelaFinanceira() {
  const {
    loading,
    listaLancamentos,
    listarContasApagar,
    listarContasAreceber
  } = useLancamentosFinanceiros();

  const handlePagar = async () => {
    // Primeira chamada: faz consulta ao servidor
    // Chamadas subsequentes: usa cache local
    await listarContasApagar();
  };

  const handleReceber = async () => {
    // Usa o mesmo cache, apenas filtra localmente
    await listarContasAreceber();
  };

  return (
    <div>
      <button onClick={handlePagar}>Pagar</button>
      <button onClick={handleReceber}>Receber</button>
      
      {loading && <p>Carregando...</p>}
      
      <ul>
        {listaLancamentos.map(lancamento => (
          <li key={lancamento.id}>
            {lancamento.descricao} - R$ {lancamento.valor}
          </li>
        ))}
      </ul>
    </div>
  );
}
```

### Exemplo Avançado com Controle de Cache

```typescript
function TelaAvancada() {
  const {
    dadosCarregados,
    recarregarDados,
    filtroAtivo,
    listarContasApagar
  } = useLancamentosFinanceiros();

  const handlePagarComValidacao = async () => {
    // Verifica se precisa recarregar dados
    if (!dadosCarregados) {
      console.log('Primeira consulta - buscando do servidor');
    } else {
      console.log('Usando cache local');
    }
    
    await listarContasApagar();
  };

  const forcarAtualizacao = async () => {
    // Força nova consulta ignorando cache
    await recarregarDados();
  };

  return (
    <div>
      <p>Status: {dadosCarregados ? 'Cache ativo' : 'Cache vazio'}</p>
      <p>Filtro atual: {filtroAtivo}</p>
      
      <button onClick={handlePagarComValidacao}>Pagar</button>
      <button onClick={forcarAtualizacao}>Atualizar Dados</button>
    </div>
  );
}
```

## 🔧 Configuração da API

### Endpoint Esperado

```typescript
// POST /lancamentos-financeiros/listar
{
  "empresa": { "id": 1 },
  "filtroPor": "DPL", // Data Previsão Lançamento
  "paginacaoVo": {
    "limiteConsulta": 1000,
    "paginaAtual": 1
  }
}
```

### Resposta Esperada

```typescript
{
  "tipo": "success",
  "objeto": [
    {
      "id": 1,
      "descricao": "Pagamento fornecedor",
      "valor": 1500.00,
      "tipoLancamentoFinanceiroId": 2, // 1=Receita, 2=Despesa
      "efetivado": false,
      "dataLancamento": "2025-01-15",
      "pessoaNome": "Fornecedor ABC",
      "contaNome": "Banco do Brasil",
      "localNome": "Matriz",
      "categoriaNome": "Materiais"
    }
  ]
}
```

## 📊 Tipos de Lançamento

```typescript
export const TIPO_LANCAMENTO_FINANCEIRO = {
  RECEITA: 1,        // Contas a Receber
  DESPESA: 2,        // Contas a Pagar
  TRANSFERENCIA: 3,  // Transferências
  SALDO_INICIAL: 4   // Saldos Iniciais
};
```

## 🎨 Filtros Disponíveis

```typescript
export const FILTRO_TIPOS = {
  CONTAS_A_PAGAR: 'CONTAS_A_PAGAR',      // Apenas despesas
  CONTAS_A_RECEBER: 'CONTAS_A_RECEBER',  // Apenas receitas
  EXTRATO: 'EXTRATO',                    // Todos os tipos
  TRANSFERENCIAS: 'TRANSFERENCIAS',      // Apenas transferências
  SALDOS_INICIAIS: 'SALDOS_INICIAIS'     // Apenas saldos iniciais
};
```

## 🔄 Fluxo de Funcionamento

1. **Primeira Interação**
   - Usuário clica em "Pagar" ou "Receber"
   - Hook verifica que `dadosCarregados = false`
   - Faz consulta ao servidor buscando TODOS os lançamentos
   - Armazena dados em `listaLancamentosOriginal`
   - Aplica filtro local e popula `listaLancamentos`
   - Define `dadosCarregados = true`

2. **Interações Subsequentes**
   - Usuário alterna entre "Pagar" e "Receber"
   - Hook verifica que `dadosCarregados = true`
   - Aplica apenas filtros locais sobre `listaLancamentosOriginal`
   - Nenhuma nova consulta é feita

3. **Atualização Forçada**
   - Usuário clica em "Recarregar"
   - Hook define `dadosCarregados = false`
   - Próxima interação força nova consulta

## 🎯 Benefícios

- **Performance**: Reduz drasticamente o número de consultas ao servidor
- **UX**: Navegação instantânea entre filtros
- **Compatibilidade**: Mantém a mesma lógica do sistema legacy
- **Flexibilidade**: Permite forçar atualizações quando necessário
- **Escalabilidade**: Suporta grandes volumes de dados em cache

## 🧪 Testando

Para testar a implementação, use o componente de demonstração:

```typescript
import LancamentosDemo from '@/pages/Financial/components/LancamentosDemo';

// Renderize o componente para ver:
// - Contador de consultas realizadas
// - Status do cache
// - Filtros aplicados
// - Lista de lançamentos
```

## 🔗 Integração com Tela Existente

Para integrar com a tela `Financial.tsx` existente:

```typescript
// Substitua os hooks atuais
const { accounts: payableAccounts } = useAccountsPayable();
const { accounts: receivableAccounts } = useAccountsReceivable();

// Por:
const {
  listaLancamentos,
  listarContasApagar,
  listarContasAreceber
} = useLancamentosFinanceiros();

// E adapte a lógica de filtros para usar os dados unificados
```

## 📝 Próximos Passos

1. **Integrar com API real**: Ajustar endpoint e estrutura de dados
2. **Adicionar filtros avançados**: Data, categoria, local, etc.
3. **Implementar ações**: Marcar como pago/recebido
4. **Adicionar paginação**: Para grandes volumes de dados
5. **Persistir cache**: LocalStorage ou SessionStorage para manter dados entre sessões
