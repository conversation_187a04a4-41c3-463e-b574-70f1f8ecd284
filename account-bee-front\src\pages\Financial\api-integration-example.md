# Integração com API - Exemplo de Implementação

Este documento mostra como adaptar o backend (NestJS) para funcionar com a nova implementação de lançamentos financeiros.

## 🎯 Endpoint Necessário

### POST `/lancamentos-financeiros/listar`

Este endpoint deve retornar TODOS os lançamentos financeiros de uma empresa, seguindo o padrão do account-bee-legacy.

#### Request Body

```typescript
interface FiltroLancamentoFinanceiro {
  empresa: {
    id: number;
  };
  filtroPor?: 'DPL' | 'DFP' | 'DCL'; // Data Previsão | Data Fechamento PDV | Data Competência
  dataInicio?: string;
  dataFim?: string;
  tipoLancamentoFinanceiro?: {
    id: number; // 1=Receita, 2=Despesa, 3=Transferência, 4=Saldo Inicial
  };
  locais?: Array<{ id: number }>;
  contas?: Array<{ id: number }>;
  categorias?: Array<{ id: number }>;
  paginacaoVo?: {
    limiteConsulta: number;
    paginaAtual: number;
  };
}
```

#### Response

```typescript
interface ApiResponse {
  tipo: 'success' | 'error';
  mensagem?: string;
  objeto: LancamentoFinanceiro[];
}

interface LancamentoFinanceiro {
  id: number;
  descricao: string;
  dataLancamento?: string; // ISO date
  dataCompetencia?: string; // ISO date
  valorBruto?: number;
  valor: number;
  observacao?: string;
  efetivado?: boolean;
  conciliado?: boolean;
  empresaId: number;
  pessoaId: number;
  pessoaNome?: string;
  contaId?: number;
  contaNome?: string;
  localId?: number;
  localNome?: string;
  fornecedorId?: number;
  fornecedorNome?: string;
  tipoLancamentoFinanceiroId: number; // 1=Receita, 2=Despesa
  categoriaLctoFinanceiroId?: number;
  categoriaNome?: string;
  planoContaCredito?: number;
  planoContaCreditoNome?: string;
  dataHoraUsuarioInc: string; // ISO datetime
  dataHoraUsuarioAlt: string; // ISO datetime
  usuarioInc: string;
  usuarioAlt: string;
}
```

## 🔧 Implementação no Backend (NestJS)

### Controller

```typescript
// src/lancamentos-financeiros/lancamentos-financeiros.controller.ts

import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { LancamentosFinanceirosService } from './lancamentos-financeiros.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { FiltroLancamentoFinanceiroDto } from './dto/filtro-lancamento-financeiro.dto';

@Controller('lancamentos-financeiros')
@UseGuards(JwtAuthGuard)
export class LancamentosFinanceirosController {
  constructor(
    private readonly lancamentosService: LancamentosFinanceirosService
  ) {}

  @Post('listar')
  async listar(@Body() filtro: FiltroLancamentoFinanceiroDto) {
    try {
      const lancamentos = await this.lancamentosService.listarTodos(filtro);
      
      return {
        tipo: 'success',
        objeto: lancamentos
      };
    } catch (error) {
      return {
        tipo: 'error',
        mensagem: error.message || 'Erro ao listar lançamentos',
        objeto: []
      };
    }
  }
}
```

### Service

```typescript
// src/lancamentos-financeiros/lancamentos-financeiros.service.ts

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LancamentoFinanceiro } from './entities/lancamento-financeiro.entity';
import { FiltroLancamentoFinanceiroDto } from './dto/filtro-lancamento-financeiro.dto';

@Injectable()
export class LancamentosFinanceirosService {
  constructor(
    @InjectRepository(LancamentoFinanceiro)
    private lancamentoRepository: Repository<LancamentoFinanceiro>,
  ) {}

  async listarTodos(filtro: FiltroLancamentoFinanceiroDto): Promise<LancamentoFinanceiro[]> {
    const queryBuilder = this.lancamentoRepository
      .createQueryBuilder('lancamento')
      .leftJoinAndSelect('lancamento.pessoa', 'pessoa')
      .leftJoinAndSelect('lancamento.conta', 'conta')
      .leftJoinAndSelect('lancamento.local', 'local')
      .leftJoinAndSelect('lancamento.fornecedor', 'fornecedor')
      .leftJoinAndSelect('lancamento.tipoLancamentoFinanceiro', 'tipo')
      .leftJoinAndSelect('lancamento.categoriaLancamento', 'categoria')
      .leftJoinAndSelect('lancamento.planoContaCreditoEntity', 'planoCredito')
      .where('lancamento.empresaId = :empresaId', { empresaId: filtro.empresa.id })
      .andWhere('lancamento.isExcluido = :isExcluido', { isExcluido: 'N' });

    // Filtro por tipo de lançamento
    if (filtro.tipoLancamentoFinanceiro?.id) {
      queryBuilder.andWhere('lancamento.tipoLancamentoFinanceiroId = :tipoId', {
        tipoId: filtro.tipoLancamentoFinanceiro.id
      });
    }

    // Filtro por data
    if (filtro.dataInicio && filtro.dataFim) {
      const campoData = this.getCampoDataPorFiltro(filtro.filtroPor);
      queryBuilder.andWhere(`lancamento.${campoData} BETWEEN :dataInicio AND :dataFim`, {
        dataInicio: filtro.dataInicio,
        dataFim: filtro.dataFim
      });
    }

    // Filtro por locais
    if (filtro.locais?.length > 0) {
      const localIds = filtro.locais.map(l => l.id);
      queryBuilder.andWhere('lancamento.localId IN (:...localIds)', { localIds });
    }

    // Filtro por contas
    if (filtro.contas?.length > 0) {
      const contaIds = filtro.contas.map(c => c.id);
      queryBuilder.andWhere('lancamento.contaId IN (:...contaIds)', { contaIds });
    }

    // Filtro por categorias
    if (filtro.categorias?.length > 0) {
      const categoriaIds = filtro.categorias.map(c => c.id);
      queryBuilder.andWhere('lancamento.categoriaLctoFinanceiroId IN (:...categoriaIds)', { categoriaIds });
    }

    // Ordenação
    const campoOrdenacao = this.getCampoDataPorFiltro(filtro.filtroPor);
    queryBuilder
      .orderBy(`lancamento.${campoOrdenacao}`, 'DESC')
      .addOrderBy('lancamento.id', 'DESC');

    // Paginação (se especificada)
    if (filtro.paginacaoVo?.limiteConsulta) {
      queryBuilder.limit(filtro.paginacaoVo.limiteConsulta);
      
      if (filtro.paginacaoVo.paginaAtual > 1) {
        const offset = (filtro.paginacaoVo.paginaAtual - 1) * filtro.paginacaoVo.limiteConsulta;
        queryBuilder.offset(offset);
      }
    }

    const lancamentos = await queryBuilder.getMany();

    // Mapeia os dados para incluir nomes relacionados
    return lancamentos.map(lancamento => ({
      ...lancamento,
      pessoaNome: lancamento.pessoa?.nome,
      contaNome: lancamento.conta?.descricao,
      localNome: lancamento.local?.descricao,
      fornecedorNome: lancamento.fornecedor?.nome,
      categoriaNome: lancamento.categoriaLancamento?.descricao,
      planoContaCreditoNome: lancamento.planoContaCreditoEntity?.descricao,
    }));
  }

  private getCampoDataPorFiltro(filtroPor?: string): string {
    switch (filtroPor) {
      case 'DCL':
        return 'dataCompetencia';
      case 'DFP':
        return 'dataFechamentoPdv'; // Se existir este campo
      case 'DPL':
      default:
        return 'dataLancamento';
    }
  }
}
```

### DTO

```typescript
// src/lancamentos-financeiros/dto/filtro-lancamento-financeiro.dto.ts

import { IsOptional, IsObject, IsString, IsArray, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

class EmpresaDto {
  @IsNumber()
  id: number;
}

class TipoLancamentoDto {
  @IsNumber()
  id: number;
}

class LocalDto {
  @IsNumber()
  id: number;
}

class ContaDto {
  @IsNumber()
  id: number;
}

class CategoriaDto {
  @IsNumber()
  id: number;
}

class PaginacaoDto {
  @IsNumber()
  limiteConsulta: number;

  @IsNumber()
  paginaAtual: number;
}

export class FiltroLancamentoFinanceiroDto {
  @IsObject()
  @Type(() => EmpresaDto)
  empresa: EmpresaDto;

  @IsOptional()
  @IsString()
  filtroPor?: 'DPL' | 'DFP' | 'DCL';

  @IsOptional()
  @IsString()
  dataInicio?: string;

  @IsOptional()
  @IsString()
  dataFim?: string;

  @IsOptional()
  @IsObject()
  @Type(() => TipoLancamentoDto)
  tipoLancamentoFinanceiro?: TipoLancamentoDto;

  @IsOptional()
  @IsArray()
  @Type(() => LocalDto)
  locais?: LocalDto[];

  @IsOptional()
  @IsArray()
  @Type(() => ContaDto)
  contas?: ContaDto[];

  @IsOptional()
  @IsArray()
  @Type(() => CategoriaDto)
  categorias?: CategoriaDto[];

  @IsOptional()
  @IsObject()
  @Type(() => PaginacaoDto)
  paginacaoVo?: PaginacaoDto;
}
```

## 🔄 Migração do Legacy

### Mapeamento de Campos

| Legacy | Novo Frontend | Backend Entity |
|--------|---------------|----------------|
| `tipoLancamentoDesc` | `tipoLancamentoFinanceiroId` | `tipoLancamentoFinanceiroId` |
| `localDescricao` | `localNome` | `local.descricao` |
| `pessoaNome` | `pessoaNome` | `pessoa.nome` |
| `fornecedorNome` | `fornecedorNome` | `fornecedor.nome` |
| `contaDescricao` | `contaNome` | `conta.descricao` |
| `categoriaDescricao` | `categoriaNome` | `categoriaLancamento.descricao` |

### Tipos de Lançamento

```typescript
export enum TipoLancamentoFinanceiro {
  RECEITA = 1,
  DESPESA = 2,
  TRANSFERENCIA = 3,
  SALDO_INICIAL = 4
}
```

## 🧪 Teste da Integração

### Exemplo de Request

```bash
curl -X POST http://localhost:3000/lancamentos-financeiros/listar \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "empresa": { "id": 1 },
    "filtroPor": "DPL",
    "paginacaoVo": {
      "limiteConsulta": 1000,
      "paginaAtual": 1
    }
  }'
```

### Exemplo de Response

```json
{
  "tipo": "success",
  "objeto": [
    {
      "id": 1,
      "descricao": "Pagamento fornecedor ABC",
      "dataLancamento": "2025-01-15T00:00:00.000Z",
      "dataCompetencia": "2025-01-15T00:00:00.000Z",
      "valor": 1500.00,
      "valorBruto": 1500.00,
      "efetivado": false,
      "tipoLancamentoFinanceiroId": 2,
      "empresaId": 1,
      "pessoaId": 123,
      "pessoaNome": "Fornecedor ABC Ltda",
      "contaId": 1,
      "contaNome": "Banco do Brasil",
      "localId": 1,
      "localNome": "Matriz São Paulo",
      "categoriaLctoFinanceiroId": 5,
      "categoriaNome": "Materiais de Escritório",
      "dataHoraUsuarioInc": "2025-01-10T10:30:00.000Z",
      "dataHoraUsuarioAlt": "2025-01-10T10:30:00.000Z",
      "usuarioInc": "admin",
      "usuarioAlt": "admin"
    }
  ]
}
```

## 🚀 Deploy e Configuração

### Variáveis de Ambiente

```env
# .env
DATABASE_URL=postgresql://user:password@localhost:5432/accountbee
JWT_SECRET=your-jwt-secret
API_PORT=3000
```

### Configuração do CORS

```typescript
// main.ts
app.enableCors({
  origin: ['http://localhost:5173', 'https://your-frontend-domain.com'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
});
```

## 📊 Performance

### Otimizações Recomendadas

1. **Índices no Banco**:
   ```sql
   CREATE INDEX idx_lancamento_empresa_data ON lancamento_financeiro(empresa_id, data_lancamento);
   CREATE INDEX idx_lancamento_tipo ON lancamento_financeiro(tipo_lancamento_financeiro_id);
   ```

2. **Cache Redis** (opcional):
   ```typescript
   @Injectable()
   export class LancamentosFinanceirosService {
     async listarTodos(filtro: FiltroLancamentoFinanceiroDto) {
       const cacheKey = `lancamentos:${filtro.empresa.id}`;
       
       // Verifica cache
       const cached = await this.redis.get(cacheKey);
       if (cached) {
         return JSON.parse(cached);
       }
       
       // Busca do banco
       const lancamentos = await this.queryDatabase(filtro);
       
       // Salva no cache por 5 minutos
       await this.redis.setex(cacheKey, 300, JSON.stringify(lancamentos));
       
       return lancamentos;
     }
   }
   ```

3. **Paginação Inteligente**:
   - Para empresas com muitos lançamentos, implemente paginação no backend
   - Use `limiteConsulta: 1000` como padrão
   - Permita que o frontend solicite mais dados conforme necessário
