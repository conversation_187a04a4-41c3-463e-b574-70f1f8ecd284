import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { LancamentosFinanceirosService } from './lancamentos-financeiros.service';
import {
  CreateLancamentoFinanceiroDto,
  LancamentoFinanceiroResponseDto,
  CostCenterAllocationDto
} from './dto';

@ApiTags('lancamentos-financeiros')
@Controller('lancamentos-financeiros')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LancamentosFinanceirosController {
  constructor(
    private readonly lancamentosService: LancamentosFinanceirosService,
  ) {}

  @Post('receitas')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Criar nova receita (lançamento financeiro tipo 1)' })
  @ApiResponse({ 
    status: 201, 
    description: 'Receita criada com sucesso', 
    type: LancamentoFinanceiroResponseDto 
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async createReceita(
    @Body() createDto: CreateLancamentoFinanceiroDto,
    @Request() req: any,
  ): Promise<LancamentoFinanceiroResponseDto> {
    // Garantir que é uma receita
    createDto.tipoLancamentoFinanceiroId = 1;
    createDto.empresaId = req.user.empresaId;
    
    return this.lancamentosService.create(createDto, req.user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar lançamento financeiro por ID' })
  @ApiParam({ name: 'id', description: 'ID do lançamento financeiro' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lançamento encontrado', 
    type: LancamentoFinanceiroResponseDto 
  })
  @ApiResponse({ status: 404, description: 'Lançamento não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async findOne(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<LancamentoFinanceiroResponseDto> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.findById(+id, empresaId);
  }

  @Put(':id/alocacoes-centro-custo')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Atualizar alocações de centro de custo de um lançamento' })
  @ApiParam({ name: 'id', description: 'ID do lançamento financeiro' })
  @ApiResponse({ status: 204, description: 'Alocações atualizadas com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Lançamento não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async updateCostCenterAllocations(
    @Param('id') id: string,
    @Body() allocations: CostCenterAllocationDto[],
    @Request() req: any,
  ): Promise<void> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.updateCostCenterAllocations(+id, allocations, empresaId, req.user);
  }

  @Get(':id/alocacoes-centro-custo')
  @ApiOperation({ summary: 'Listar alocações de centro de custo de um lançamento' })
  @ApiParam({ name: 'id', description: 'ID do lançamento financeiro' })
  @ApiResponse({ status: 200, description: 'Lista de alocações' })
  @ApiResponse({ status: 404, description: 'Lançamento não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async getCostCenterAllocations(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<any[]> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.getCostCenterAllocations(+id, empresaId);
  }

  @Delete(':lancamentoId/alocacoes-centro-custo/:allocationId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Excluir uma alocação de centro de custo específica' })
  @ApiParam({ name: 'lancamentoId', description: 'ID do lançamento financeiro' })
  @ApiParam({ name: 'allocationId', description: 'ID da alocação de centro de custo' })
  @ApiResponse({ status: 204, description: 'Alocação excluída com sucesso' })
  @ApiResponse({ status: 404, description: 'Alocação não encontrada' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async deleteCostCenterAllocation(
    @Param('lancamentoId') lancamentoId: string,
    @Param('allocationId') allocationId: string,
    @Request() req: any,
  ): Promise<void> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.deleteCostCenterAllocation(+lancamentoId, +allocationId, empresaId, req.user);
  }
}
