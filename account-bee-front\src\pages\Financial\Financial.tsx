import { useState, useEffect, useMemo } from 'react';
import { Plus, Edit, Check, Calendar, AlertTriangle, ChevronLeft, ChevronRight, X, Filter, RefreshCw } from 'lucide-react';
import { Layout } from '@/components/layout';
import { Button, BulkActionsToolbar } from '@/components/ui/forms';
import { Card } from '@/components/ui/layout';
import { Badge } from '@/components/ui/shadcn/badge';
import { Checkbox } from '@/components/ui/forms';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/shadcn/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/shadcn/table';
import { TablePagination } from '@/components/ui/navigation/TablePagination/TablePagination';
import { useLancamentosFinanceiros, FILTRO_TIPOS, TIPO_LANCAMENTO_FINANCEIRO } from '@/hooks/useLancamentosFinanceiros';
import { AccountPayableFormDialog } from '@/pages/AccountsPayable/components/AccountPayableFormDialog';
import { AccountReceivableFormDialog } from '@/pages/AccountsReceivable/components/AccountReceivableFormDialog';
import { ReceitaFormDialog } from './components/ReceitaFormDialog';
import { ConfirmDialog } from '@/components/ui/feedback';
import { useToast } from '@/hooks/use-toast';
import { AccountPayable, AccountReceivable } from '@/types/accounts';
import { AdvancedFiltersModal, AdvancedFiltersData } from '@/components/ui/forms/AdvancedFiltersModal';
import { exportToExcel } from '@/utils/exportUtils';

const MONTHS = [
  'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
  'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
];

export default function Financial() {
  // Novo hook integrado que replica o comportamento do legacy
  const {
    loading,
    error,
    listaLancamentos,
    filtroAtivo,
    dadosCarregados,
    listarContasApagar,
    listarContasAreceber,
    recarregarDados,
    marcarComoEfetivado,
    marcarComoNaoEfetivado,
    marcarMultiplosComoEfetivados
  } = useLancamentosFinanceiros();

  const { toast } = useToast();

  const [activeTab, setActiveTab] = useState<'pagar' | 'receber'>('pagar');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFiltersData>({});
  const [isAdvancedFiltersOpen, setIsAdvancedFiltersOpen] = useState(false);

  // Modal states
  const [isCreatePayableOpen, setIsCreatePayableOpen] = useState(false);
  const [isCreateReceitaOpen, setIsCreateReceitaOpen] = useState(false);
  const [editingPayable, setEditingPayable] = useState<AccountPayable | null>(null);
  const [editingReceivable, setEditingReceivable] = useState<AccountReceivable | null>(null);

  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Carrega dados automaticamente na primeira renderização
  useEffect(() => {
    if (!dadosCarregados) {
      handleTabChange('pagar');
    }
  }, [dadosCarregados]);

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  // Função para lidar com mudança de aba (baseada no legacy)
  const handleTabChange = async (tab: 'pagar' | 'receber') => {
    setActiveTab(tab);
    setSelectedIds([]);
    setCurrentPage(1);

    try {
      if (tab === 'pagar') {
        await listarContasApagar();
      } else {
        await listarContasAreceber();
      }
    } catch (err) {
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar os lançamentos.',
        variant: 'destructive',
      });
    }
  };

  // Filter accounts by month and advanced filters
  const filteredAccounts = useMemo(() => {
    return listaLancamentos.filter(lancamento => {
      const lancamentoDate = new Date(lancamento.dataLancamento || lancamento.dataCompetencia || new Date());

      // Month filter
      if (lancamentoDate.getMonth() !== currentMonth || lancamentoDate.getFullYear() !== currentYear) {
        return false;
      }

      // Advanced filters
      if (advancedFilters.category && lancamento.categoriaLctoFinanceiroId !== advancedFilters.category) {
        return false;
      }

      if (advancedFilters.location && lancamento.localNome !== advancedFilters.location) {
        return false;
      }

      if (advancedFilters.status) {
        const status = lancamento.efetivado ? (activeTab === 'pagar' ? 'paid' : 'received') : 'open';
        if (status !== advancedFilters.status) {
          return false;
        }
      }

      if (advancedFilters.account && lancamento.contaId !== advancedFilters.account) {
        return false;
      }

      if (advancedFilters.supplier) {
        const supplierField = activeTab === 'pagar' ? lancamento.fornecedorNome : lancamento.pessoaNome;
        if (!supplierField?.toLowerCase().includes(advancedFilters.supplier.toLowerCase())) {
          return false;
        }
      }

      if (advancedFilters.description) {
        if (!lancamento.descricao?.toLowerCase().includes(advancedFilters.description.toLowerCase())) {
          return false;
        }
      }

      if (advancedFilters.minAmount && lancamento.valor < advancedFilters.minAmount) {
        return false;
      }

      if (advancedFilters.maxAmount && lancamento.valor > advancedFilters.maxAmount) {
        return false;
      }

      return true;
    });
  }, [listaLancamentos, currentMonth, currentYear, advancedFilters, activeTab]);

  // Reset page when tab, month, filters, or page size change
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, currentMonth, currentYear, advancedFilters, pageSize]);

  // Pagination
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredAccounts.slice(startIndex, endIndex);
  }, [filteredAccounts, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredAccounts.length / pageSize);

  // Calculate totals
  const totals = useMemo(() => {
    const efetivados = filteredAccounts.filter(l => l.efetivado);
    const abertos = filteredAccounts.filter(l => !l.efetivado);
    const vencidos = filteredAccounts.filter(l => {
      if (l.efetivado) return false;
      const hoje = new Date();
      const dataVencimento = new Date(l.dataLancamento || l.dataCompetencia || new Date());
      return dataVencimento < hoje;
    });
    const selecionados = filteredAccounts.filter(l => selectedIds.includes(l.id.toString()));

    return {
      selected: selecionados.reduce((sum, l) => sum + l.valor, 0),
      paid: efetivados.reduce((sum, l) => sum + l.valor, 0),
      open: abertos.reduce((sum, l) => sum + l.valor, 0),
      overdue: vencidos.reduce((sum, l) => sum + l.valor, 0),
      total: filteredAccounts.reduce((sum, l) => sum + l.valor, 0),
    };
  }, [filteredAccounts, selectedIds]);

  // Utility functions
  const formatCurrency = (value: number) => {
    return value.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit'
    });
  };

  const getStatusBadge = (lancamento: any) => {
    const statusLabel = activeTab === 'pagar' ? 'Pago' : 'Recebido';

    if (lancamento.efetivado) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800 text-xs px-2 py-0">
          <Check className="w-3 h-3 mr-1" />
          {statusLabel}
        </Badge>
      );
    }

    // Verifica se está vencido
    const hoje = new Date();
    const dataVencimento = new Date(lancamento.dataLancamento || lancamento.dataCompetencia);

    if (dataVencimento < hoje) {
      return (
        <Badge variant="destructive" className="text-xs px-2 py-0">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Vencido
        </Badge>
      );
    }

    return (
      <Badge variant="secondary" className="text-xs px-2 py-0">
        <Calendar className="w-3 h-3 mr-1" />
        Em Aberto
      </Badge>
    );
  };

  const handleSelectItem = (id: string) => {
    setSelectedIds(prev =>
      prev.includes(id)
        ? prev.filter(i => i !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedIds.length === paginatedData.length && paginatedData.length > 0) {
      setSelectedIds([]);
    } else {
      setSelectedIds(paginatedData.map(item => item.id.toString()));
    }
  };

  const handleMarkAsPaid = async (id: string) => {
    try {
      await marcarComoEfetivado(parseInt(id));

      toast({
        title: 'Status atualizado',
        description: `Item marcado como ${activeTab === 'pagar' ? 'pago' : 'recebido'}.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar o status.',
        variant: 'destructive',
      });
    }
  };

  const handleMarkAsUnpaid = async (id: string) => {
    try {
      await marcarComoNaoEfetivado(parseInt(id));

      toast({
        title: 'Status atualizado',
        description: `Item marcado como não ${activeTab === 'pagar' ? 'pago' : 'recebido'}.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar o status.',
        variant: 'destructive',
      });
    }
  };

  const handleMarkMultipleAsPaid = async () => {
    try {
      const ids = selectedIds.map(id => parseInt(id));
      await marcarMultiplosComoEfetivados(ids);

      setSelectedIds([]);
      toast({
        title: 'Status atualizado',
        description: `${selectedIds.length} item(s) marcado(s) como ${activeTab === 'pagar' ? 'pago(s)' : 'recebido(s)'}.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar os status.',
        variant: 'destructive',
      });
    }
  };

  const handleStatusChange = async (status: 'open' | 'paid' | 'received' | 'overdue') => {
    try {
      // TODO: Implementar função para atualizar status múltiplos
      // await updateMultipleLancamentosStatus(selectedIds, status);

      setSelectedIds([]);

      const statusLabels = {
        'open': 'Em Aberto',
        'paid': 'Pago',
        'received': 'Recebido',
        'overdue': 'Vencido'
      };

      toast({
        title: 'Status atualizado',
        description: `${selectedIds.length} item(s) marcado(s) como ${statusLabels[status]}.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar os status.',
        variant: 'destructive',
      });
    }
  };

  const handleBulkDelete = async () => {
    try {
      // TODO: Implementar função para excluir múltiplos lançamentos
      // await deleteMultipleLancamentos(selectedIds);

      setSelectedIds([]);

      toast({
        title: 'Itens excluídos',
        description: `${selectedIds.length} item(s) excluído(s) com sucesso.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível excluir os itens.',
        variant: 'destructive',
      });
    }
  };

  const handleRecarregar = async () => {
    try {
      await recarregarDados();
      // Reaplica o filtro atual
      if (activeTab === 'pagar') {
        await listarContasApagar();
      } else {
        await listarContasAreceber();
      }

      toast({
        title: 'Dados atualizados',
        description: 'Os lançamentos foram recarregados com sucesso.',
      });
    } catch (err) {
      toast({
        title: 'Erro',
        description: 'Não foi possível recarregar os dados.',
        variant: 'destructive',
      });
    }
  };

  const prepareExportData = (lancamentos: any[]) => {
    return lancamentos.map(lancamento => ({
      Data: formatDate(lancamento.dataLancamento || lancamento.dataCompetencia || new Date()),
      Descrição: lancamento.descricao,
      [activeTab === 'pagar' ? 'Fornecedor' : 'Cliente']: activeTab === 'pagar' ?
        (lancamento.fornecedorNome || lancamento.pessoaNome) :
        lancamento.pessoaNome,
      Local: lancamento.localNome || '',
      Conta: lancamento.contaNome || '',
      Valor: lancamento.valor,
      Status: lancamento.efetivado ?
        (activeTab === 'pagar' ? 'Pago' : 'Recebido') :
        'Em Aberto',
      Categoria: lancamento.categoriaNome || '',
    }));
  };

  const handleExport = () => {
    // Se há itens selecionados, exporta apenas os selecionados. Caso contrário, exporta todos
    const lancamentosToExport = selectedIds.length > 0
      ? filteredAccounts.filter(lancamento => selectedIds.includes(lancamento.id.toString()))
      : filteredAccounts;

    const exportData = prepareExportData(lancamentosToExport);
    const filename = selectedIds.length > 0
      ? `${activeTab === 'pagar' ? 'contas-a-pagar' : 'contas-a-receber'}-selecionadas-${MONTHS[currentMonth]}-${currentYear}`
      : `${activeTab === 'pagar' ? 'contas-a-pagar' : 'contas-a-receber'}-${MONTHS[currentMonth]}-${currentYear}`;

    if (exportToExcel(exportData, filename, activeTab === 'pagar' ? 'Contas a Pagar' : 'Contas a Receber')) {
      const description = selectedIds.length > 0
        ? `${lancamentosToExport.length} item(s) selecionado(s) exportado(s) para Excel.`
        : `${lancamentosToExport.length} item(s) exportado(s) para Excel.`;

      toast({
        title: 'Exportação concluída',
        description,
      });
    } else {
      toast({
        title: 'Erro na exportação',
        description: 'Não foi possível exportar os dados.',
        variant: 'destructive',
      });
    }
  };



  const handleApplyFilters = (filters: AdvancedFiltersData) => {
    setAdvancedFilters(filters);
  };

  const handleClearFilters = () => {
    setAdvancedFilters({});
  };

  const hasActiveFilters = Object.keys(advancedFilters).some(key => 
    advancedFilters[key as keyof AdvancedFiltersData] !== undefined
  );

  return (
    <Layout>
      <div className="h-screen flex flex-col overflow-hidden">
        {/* Compact Header */}
        <div className="flex items-center justify-between px-6 py-3 border-b">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold">Financeiro</h1>
            {filtroAtivo && (
              <Badge variant="outline" className="text-sm">
                {filtroAtivo === FILTRO_TIPOS.CONTAS_A_PAGAR ? 'Contas a Pagar' : 'Contas a Receber'}
              </Badge>
            )}
            {dadosCarregados && (
              <Badge variant="secondary" className="text-xs">
                Cache Ativo
              </Badge>
            )}
          </div>

          {/* Month Navigation */}
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" onClick={goToPreviousMonth}>
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <div className="text-lg font-semibold min-w-[100px] text-center">
              {MONTHS[currentMonth]}/{currentYear}
            </div>
            <Button variant="outline" size="sm" onClick={goToNextMonth}>
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRecarregar}
              disabled={loading}
              className="gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Recarregar
            </Button>
            <Button
              variant={hasActiveFilters ? "default" : "outline"}
              size="sm"
              className="gap-2"
              onClick={() => setIsAdvancedFiltersOpen(true)}
            >
              <Filter className="w-4 h-4" />
              Filtros Avançados
              {hasActiveFilters && (
                <span className="bg-white text-blue-600 text-xs rounded-full px-1.5 py-0.5 ml-1">
                  {Object.keys(advancedFilters).length}
                </span>
              )}
            </Button>
            <BulkActionsToolbar
              type={activeTab === 'pagar' ? 'payable' : 'receivable'}
              selectedCount={selectedIds.length}
              selectedData={filteredAccounts.filter(lancamento => selectedIds.includes(lancamento.id.toString()))}
              onStatusChange={handleStatusChange}
              onBulkDelete={handleBulkDelete}
              onExport={handleExport}
              disabled={loading}
            />
          </div>
        </div>

        {/* Compact Tabs */}
        <div className="px-6 py-2 border-b">
          <Tabs value={activeTab} onValueChange={(value) => handleTabChange(value as 'pagar' | 'receber')}>
            <div className="flex items-center justify-between">
              <TabsList className="grid w-auto grid-cols-2">
                <TabsTrigger value="pagar" className="text-sm" disabled={loading}>
                  Pagar ({listaLancamentos.filter(l => l.tipoLancamentoFinanceiroId === TIPO_LANCAMENTO_FINANCEIRO.DESPESA).length})
                </TabsTrigger>
                <TabsTrigger value="receber" className="text-sm" disabled={loading}>
                  Receber ({listaLancamentos.filter(l => l.tipoLancamentoFinanceiroId === TIPO_LANCAMENTO_FINANCEIRO.RECEITA).length})
                </TabsTrigger>
              </TabsList>

              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={() => activeTab === 'pagar' ? setIsCreatePayableOpen(true) : setIsCreateReceitaOpen(true)}
                  className="gap-2"
                  disabled={loading}
                >
                  <Plus className="w-4 h-4" />
                  Nova {activeTab === 'pagar' ? 'Despesa' : 'Receita'}
                </Button>
              </div>
            </div>
          </Tabs>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2" />
              <p>Carregando lançamentos...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-red-600">
              <AlertTriangle className="w-8 h-8 mx-auto mb-2" />
              <p>{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRecarregar}
                className="mt-2"
              >
                Tentar novamente
              </Button>
            </div>
          </div>
        )}

        {/* Table Container */}
        {!loading && !error && (
          <div className="flex-1 flex flex-col px-6 py-2 overflow-hidden">
            <div className="rounded-md border flex-1 overflow-auto">
              <Table>
              <TableHeader className="sticky top-0 bg-background">
                <TableRow className="h-10">
                  <TableHead className="w-10">
                    <Checkbox 
                      checked={selectedIds.length === paginatedData.length && paginatedData.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead className="text-xs">Data</TableHead>
                  <TableHead className="text-xs">Descrição</TableHead>
                  <TableHead className="text-xs">{activeTab === 'pagar' ? 'Fornecedor' : 'Cliente'}</TableHead>
                  <TableHead className="text-xs">Local</TableHead>
                  <TableHead className="text-xs">Conta</TableHead>
                  <TableHead className="text-xs">Categoria</TableHead>
                  <TableHead className="text-xs text-right">Valor</TableHead>
                  <TableHead className="text-xs">Status</TableHead>
                  <TableHead className="text-xs w-32">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((lancamento) => (
                  <TableRow key={lancamento.id} className="h-12">
                    <TableCell>
                      <Checkbox
                        checked={selectedIds.includes(lancamento.id.toString())}
                        onCheckedChange={() => handleSelectItem(lancamento.id.toString())}
                      />
                    </TableCell>
                    <TableCell className="text-sm">
                      {formatDate(lancamento.dataLancamento || lancamento.dataCompetencia || new Date())}
                    </TableCell>
                    <TableCell className="text-sm max-w-[200px] truncate" title={lancamento.descricao}>
                      {lancamento.descricao}
                    </TableCell>
                    <TableCell className="text-sm">
                      {activeTab === 'pagar' ?
                        (lancamento.fornecedorNome || lancamento.pessoaNome) :
                        lancamento.pessoaNome || '-'}
                    </TableCell>
                    <TableCell className="text-sm">{lancamento.localNome || '-'}</TableCell>
                    <TableCell className="text-sm">{lancamento.contaNome || '-'}</TableCell>
                    <TableCell className="text-sm">{lancamento.categoriaNome || '-'}</TableCell>
                    <TableCell className="text-sm text-right font-mono">
                      {formatCurrency(lancamento.valor)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(lancamento)}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        {lancamento.efetivado ? (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMarkAsUnpaid(lancamento.id.toString())}
                            className="h-8 w-8 p-0"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        ) : (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMarkAsPaid(lancamento.id.toString())}
                            className="h-8 w-8 p-0"
                          >
                            <Check className="w-4 h-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            // TODO: Implementar edição de lançamentos
                            toast({
                              title: 'Em desenvolvimento',
                              description: 'Funcionalidade de edição será implementada em breve.',
                            });
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {/* Totals row right after the last record */}
                <TableRow>
                  <TableCell colSpan={10} className="border-t-2 bg-slate-50 p-4">
                    <div className="flex flex-wrap items-center justify-between gap-x-6 gap-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">Selecionadas:</span>
                        <span className="font-mono font-semibold text-slate-900">{formatCurrency(totals.selected)}</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">{activeTab === 'pagar' ? 'Pagas' : 'Recebidas'}:</span>
                        <span className="font-mono font-semibold text-green-600">{formatCurrency(totals.paid)}</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">A {activeTab === 'pagar' ? 'Pagar' : 'Receber'}:</span>
                        <span className="font-mono font-semibold text-blue-600">{formatCurrency(totals.open)}</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">Vencidas:</span>
                        <span className="font-mono font-semibold text-red-600">{formatCurrency(totals.overdue)}</span>
                      </div>

                      <div className="flex items-center gap-2 border-l border-slate-300 pl-4 ml-2">
                        <span className="text-slate-800 font-semibold">Total:</span>
                        <span className="font-mono font-bold text-slate-900">{formatCurrency(totals.total)}</span>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-background border border-t-0 rounded-b-lg px-4 py-3 mt-0">
              <TablePagination
                currentPage={currentPage}
                totalPages={totalPages}
                pageSize={pageSize}
                totalItems={filteredAccounts.length}
                onPageChange={setCurrentPage}
                onPageSizeChange={setPageSize}
              />
            </div>
          )}
        </div>

        {/* Modals */}
        <AccountPayableFormDialog
          open={isCreatePayableOpen}
          onOpenChange={setIsCreatePayableOpen}
          onSuccess={() => {}}
        />



        <AccountPayableFormDialog
          open={!!editingPayable}
          onOpenChange={(open) => !open && setEditingPayable(null)}
          account={editingPayable}
          onSuccess={() => {}}
        />

        <AccountReceivableFormDialog
          open={!!editingReceivable}
          onOpenChange={(open) => !open && setEditingReceivable(null)}
          account={editingReceivable}
          onSuccess={() => {}}
        />



        <ReceitaFormDialog
          open={isCreateReceitaOpen}
          onOpenChange={setIsCreateReceitaOpen}
          onSuccess={() => {
            // TODO: Recarregar dados da lista
            console.log('Receita criada com sucesso');
          }}
        />

        <AdvancedFiltersModal
          open={isAdvancedFiltersOpen}
          onOpenChange={setIsAdvancedFiltersOpen}
          onApplyFilters={handleApplyFilters}
          onClearFilters={handleClearFilters}
          currentFilters={advancedFilters}
          activeTab={activeTab}
        />
      </div>
    </Layout>
  );
} 