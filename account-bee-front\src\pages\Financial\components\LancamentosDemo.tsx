import React, { useState } from 'react';
import { Button } from '@/components/ui/forms';
import { Card } from '@/components/ui/layout';
import { Badge } from '@/components/ui/shadcn/badge';
import { useLancamentosFinanceiros, FILTRO_TIPOS } from '@/hooks/useLancamentosFinanceiros';
import { useToast } from '@/hooks/use-toast';
import { RefreshCw, DollarSign, TrendingUp, TrendingDown } from 'lucide-react';

/**
 * Componente de demonstração que mostra como usar o hook useLancamentosFinanceiros
 * seguindo o padrão do account-bee-legacy onde:
 * 1. Uma única consulta busca todos os dados
 * 2. Os dados são mantidos em cache local
 * 3. Os filtros são aplicados localmente
 */
export default function LancamentosDemo() {
  const {
    loading,
    error,
    listaLancamentos,
    listaLancamentosOriginal,
    filtroAtivo,
    dadosCarregados,
    carregarTodosLancamentos,
    listarContasApagar,
    listarContasAreceber,
    listarExtrato,
    recarregarDados
  } = useLancamentosFinanceiros();

  const { toast } = useToast();
  const [consultasRealizadas, setConsultasRealizadas] = useState(0);

  // Simula o comportamento dos botões "Pagar" e "Receber" do legacy
  const handlePagar = async () => {
    try {
      await listarContasApagar();
      
      // Incrementa contador apenas se foi feita uma nova consulta
      if (!dadosCarregados) {
        setConsultasRealizadas(prev => prev + 1);
      }
      
      toast({
        title: 'Contas a Pagar',
        description: `${listaLancamentos.length} lançamentos carregados do cache local.`,
      });
    } catch (err) {
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar as contas a pagar.',
        variant: 'destructive',
      });
    }
  };

  const handleReceber = async () => {
    try {
      await listarContasAreceber();
      
      // Incrementa contador apenas se foi feita uma nova consulta
      if (!dadosCarregados) {
        setConsultasRealizadas(prev => prev + 1);
      }
      
      toast({
        title: 'Contas a Receber',
        description: `${listaLancamentos.length} lançamentos carregados do cache local.`,
      });
    } catch (err) {
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar as contas a receber.',
        variant: 'destructive',
      });
    }
  };

  const handleExtrato = async () => {
    try {
      await listarExtrato();
      
      toast({
        title: 'Extrato Completo',
        description: `${listaLancamentos.length} lançamentos carregados.`,
      });
    } catch (err) {
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar o extrato.',
        variant: 'destructive',
      });
    }
  };

  const handleRecarregar = async () => {
    try {
      await recarregarDados();
      setConsultasRealizadas(prev => prev + 1);
      
      toast({
        title: 'Dados Recarregados',
        description: 'Nova consulta realizada com sucesso.',
      });
    } catch (err) {
      toast({
        title: 'Erro',
        description: 'Não foi possível recarregar os dados.',
        variant: 'destructive',
      });
    }
  };

  // Calcula estatísticas
  const stats = {
    totalOriginal: listaLancamentosOriginal.length,
    totalFiltrado: listaLancamentos.length,
    contasAPagar: listaLancamentosOriginal.filter(l => l.tipoLancamentoFinanceiroId === 2).length,
    contasAReceber: listaLancamentosOriginal.filter(l => l.tipoLancamentoFinanceiroId === 1).length,
  };

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Demonstração - Lançamentos Financeiros</h2>
        <p className="text-gray-600 mb-4">
          Replicando o comportamento do account-bee-legacy: uma consulta, cache local, filtros locais
        </p>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm text-gray-600">Consultas Realizadas</p>
              <p className="text-2xl font-bold">{consultasRealizadas}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-2">
            <TrendingDown className="w-5 h-5 text-red-500" />
            <div>
              <p className="text-sm text-gray-600">Contas a Pagar</p>
              <p className="text-2xl font-bold">{stats.contasAPagar}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-green-500" />
            <div>
              <p className="text-sm text-gray-600">Contas a Receber</p>
              <p className="text-2xl font-bold">{stats.contasAReceber}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-purple-500" />
            <div>
              <p className="text-sm text-gray-600">Total Registros</p>
              <p className="text-2xl font-bold">{stats.totalOriginal}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Status */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Status do Cache</h3>
          <div className="flex items-center gap-2">
            <Badge variant={dadosCarregados ? "default" : "secondary"}>
              {dadosCarregados ? "Dados em Cache" : "Cache Vazio"}
            </Badge>
            {filtroAtivo && (
              <Badge variant="outline">
                Filtro: {filtroAtivo}
              </Badge>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-600">Registros Originais:</p>
            <p className="font-semibold">{stats.totalOriginal}</p>
          </div>
          <div>
            <p className="text-gray-600">Registros Filtrados:</p>
            <p className="font-semibold">{stats.totalFiltrado}</p>
          </div>
        </div>
      </Card>

      {/* Botões de Ação */}
      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">Ações (Simulando botões do Legacy)</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <Button 
            onClick={handlePagar}
            disabled={loading}
            variant="outline"
            className="gap-2"
          >
            <TrendingDown className="w-4 h-4" />
            Pagar
          </Button>

          <Button 
            onClick={handleReceber}
            disabled={loading}
            variant="outline"
            className="gap-2"
          >
            <TrendingUp className="w-4 h-4" />
            Receber
          </Button>

          <Button 
            onClick={handleExtrato}
            disabled={loading}
            variant="outline"
            className="gap-2"
          >
            <DollarSign className="w-4 h-4" />
            Extrato
          </Button>

          <Button 
            onClick={handleRecarregar}
            disabled={loading}
            variant="default"
            className="gap-2"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Recarregar
          </Button>
        </div>
      </Card>

      {/* Explicação */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <h3 className="text-lg font-semibold mb-2 text-blue-800">Como Funciona</h3>
        <div className="text-sm text-blue-700 space-y-2">
          <p>
            <strong>1. Primeira Consulta:</strong> Ao clicar em "Pagar" ou "Receber" pela primeira vez, 
            uma consulta é feita para buscar TODOS os lançamentos financeiros.
          </p>
          <p>
            <strong>2. Cache Local:</strong> Os dados são armazenados em memória (state) e não são 
            consultados novamente até que seja feito um "Recarregar".
          </p>
          <p>
            <strong>3. Filtros Locais:</strong> Ao alternar entre "Pagar" e "Receber", apenas os 
            filtros locais são aplicados, sem nova consulta ao servidor.
          </p>
          <p>
            <strong>4. Performance:</strong> Isso garante que múltiplos cliques nos botões não 
            gerem múltiplas consultas desnecessárias.
          </p>
        </div>
      </Card>

      {/* Lista Resumida */}
      {listaLancamentos.length > 0 && (
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-4">
            Lançamentos Filtrados ({listaLancamentos.length})
          </h3>
          
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {listaLancamentos.slice(0, 10).map((lancamento) => (
              <div key={lancamento.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div>
                  <p className="font-medium text-sm">{lancamento.descricao}</p>
                  <p className="text-xs text-gray-600">
                    {lancamento.pessoaNome || lancamento.fornecedorNome || 'N/A'}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-sm">
                    R$ {lancamento.valor.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                  </p>
                  <Badge variant={lancamento.efetivado ? "default" : "secondary"} className="text-xs">
                    {lancamento.efetivado ? 'Efetivado' : 'Pendente'}
                  </Badge>
                </div>
              </div>
            ))}
            
            {listaLancamentos.length > 10 && (
              <p className="text-center text-sm text-gray-500 pt-2">
                ... e mais {listaLancamentos.length - 10} registros
              </p>
            )}
          </div>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Card className="p-4 bg-red-50 border-red-200">
          <h3 className="text-lg font-semibold mb-2 text-red-800">Erro</h3>
          <p className="text-red-700">{error}</p>
        </Card>
      )}
    </div>
  );
}
