import { useState, useMemo, useCallback, useEffect } from 'react';
import { useAuth } from './useAuth';
import { useApiClient } from '@/utils/apiClient';

export interface CostCenterAllocation {
  centroCustoId: number;
  valor: number;
  porcentagem?: number;
}

export interface CreateReceitaData {
  pessoaId?: number;
  descricao?: string;
  dataLancamento?: string;
  dataCompetencia?: string;
  contaId?: number;
  localId?: number;
  valorBruto?: number;
  valor?: number;
  categoriaLctoFinanceiroId?: number;
  alocacoesCentroCusto?: CostCenterAllocation[];
  repetirReceita?: boolean;
  tipoRepeticao?: string;
  periodicidade?: string;
  quantidadeRepeticoes?: number;
  planoContaCredito?: number;
  fornecedorId?: number;
  observacao?: string;
}

export interface LancamentoFinanceiro {
  id: number;
  descricao: string;
  dataLancamento?: Date;
  dataCompetencia?: Date;
  valorBruto?: number;
  valor: number;
  observacao?: string;
  efetivado?: boolean;
  conciliado?: boolean;
  empresaId: number;
  pessoaId: number;
  pessoaNome?: string;
  contaId?: number;
  contaNome?: string;
  localId?: number;
  localNome?: string;
  fornecedorId?: number;
  fornecedorNome?: string;
  tipoLancamentoFinanceiroId: number;
  categoriaLctoFinanceiroId?: number;
  categoriaNome?: string;
  planoContaCredito?: number;
  planoContaCreditoNome?: string;
  alocacoesCentroCusto?: any[];
  dataHoraUsuarioInc: Date;
  dataHoraUsuarioAlt: Date;
  usuarioInc: string;
  usuarioAlt: string;
}

export interface FiltroLancamentoFinanceiro {
  dataInicio?: string;
  dataFim?: string;
  tipoLancamentoFinanceiro?: {
    id: number;
  };
  empresa?: {
    id: number;
  };
  locais?: any[];
  contas?: any[];
  categorias?: any[];
  filtroPor?: string; // 'DPL' | 'DFP' | 'DCL'
  paginacaoVo?: {
    limiteConsulta: number;
    paginaAtual: number;
  };
}

// Constantes baseadas no legacy
export const TIPO_LANCAMENTO_FINANCEIRO = {
  RECEITA: 1,
  DESPESA: 2,
  TRANSFERENCIA: 3,
  SALDO_INICIAL: 4
};

export const FILTRO_TIPOS = {
  CONTAS_A_PAGAR: 'CONTAS_A_PAGAR',
  CONTAS_A_RECEBER: 'CONTAS_A_RECEBER',
  EXTRATO: 'EXTRATO',
  TRANSFERENCIAS: 'TRANSFERENCIAS',
  SALDOS_INICIAIS: 'SALDOS_INICIAIS'
};

export function useLancamentosFinanceiros() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [listaLancamentosOriginal, setListaLancamentosOriginal] = useState<LancamentoFinanceiro[]>([]);
  const [listaLancamentos, setListaLancamentos] = useState<LancamentoFinanceiro[]>([]);
  const [filtroAtivo, setFiltroAtivo] = useState<string>('');
  const [dadosCarregados, setDadosCarregados] = useState(false);

  const { token } = useAuth();

  // Memoize o apiClient para evitar recriações desnecessárias
  const apiClient = useMemo(() => {
    return useApiClient(token);
  }, [token]);

  // Função para carregar todos os lançamentos (uma única vez)
  const carregarTodosLancamentos = useCallback(async (filtro?: FiltroLancamentoFinanceiro) => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    // Se os dados já foram carregados, não carrega novamente
    if (dadosCarregados && !filtro) {
      return listaLancamentosOriginal;
    }

    setLoading(true);
    setError(null);

    try {
      // Monta o filtro padrão se não foi fornecido
      const filtroConsulta = filtro || {
        empresa: { id: 1 }, // TODO: pegar da sessão do usuário
        filtroPor: 'DPL', // Data Previsão Lançamento
        paginacaoVo: {
          limiteConsulta: 1000, // Busca todos os registros
          paginaAtual: 1
        }
      };

      const response = await apiClient.call('/lancamentos-financeiros/listar', {
        method: 'POST',
        body: JSON.stringify(filtroConsulta),
      });

      if (response.ok) {
        const data = await response.json();
        const lancamentos = Array.isArray(data) ? data : (data.objeto || []);

        setListaLancamentosOriginal(lancamentos);
        setListaLancamentos(lancamentos);
        setDadosCarregados(true);

        return lancamentos;
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao carregar lançamentos');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao carregar lançamentos';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [token, apiClient, dadosCarregados, listaLancamentosOriginal]);

  // Função para filtrar lançamentos por tipo (baseada no legacy)
  const filtrarLancamentosPorTipo = useCallback((tipo: string) => {
    if (!listaLancamentosOriginal.length) {
      return [];
    }

    let lancamentosFiltrados = [...listaLancamentosOriginal];

    switch (tipo) {
      case FILTRO_TIPOS.CONTAS_A_PAGAR:
        // Filtra apenas despesas (tipo 2)
        lancamentosFiltrados = lancamentosFiltrados.filter(
          lancamento => lancamento.tipoLancamentoFinanceiroId === TIPO_LANCAMENTO_FINANCEIRO.DESPESA
        );
        break;

      case FILTRO_TIPOS.CONTAS_A_RECEBER:
        // Filtra apenas receitas (tipo 1)
        lancamentosFiltrados = lancamentosFiltrados.filter(
          lancamento => lancamento.tipoLancamentoFinanceiroId === TIPO_LANCAMENTO_FINANCEIRO.RECEITA
        );
        break;

      case FILTRO_TIPOS.TRANSFERENCIAS:
        // Filtra apenas transferências (tipo 3)
        lancamentosFiltrados = lancamentosFiltrados.filter(
          lancamento => lancamento.tipoLancamentoFinanceiroId === TIPO_LANCAMENTO_FINANCEIRO.TRANSFERENCIA
        );
        break;

      case FILTRO_TIPOS.SALDOS_INICIAIS:
        // Filtra apenas saldos iniciais (tipo 4)
        lancamentosFiltrados = lancamentosFiltrados.filter(
          lancamento => lancamento.tipoLancamentoFinanceiroId === TIPO_LANCAMENTO_FINANCEIRO.SALDO_INICIAL
        );
        break;

      case FILTRO_TIPOS.EXTRATO:
      default:
        // Retorna todos os lançamentos
        break;
    }

    setListaLancamentos(lancamentosFiltrados);
    setFiltroAtivo(tipo);
    return lancamentosFiltrados;
  }, [listaLancamentosOriginal]);

  // Função para listar contas a pagar (baseada no legacy)
  const listarContasApagar = useCallback(async () => {
    // Se os dados não foram carregados ainda, carrega primeiro
    if (!dadosCarregados) {
      await carregarTodosLancamentos();
    }

    return filtrarLancamentosPorTipo(FILTRO_TIPOS.CONTAS_A_PAGAR);
  }, [dadosCarregados, carregarTodosLancamentos, filtrarLancamentosPorTipo]);

  // Função para listar contas a receber (baseada no legacy)
  const listarContasAreceber = useCallback(async () => {
    // Se os dados não foram carregados ainda, carrega primeiro
    if (!dadosCarregados) {
      await carregarTodosLancamentos();
    }

    return filtrarLancamentosPorTipo(FILTRO_TIPOS.CONTAS_A_RECEBER);
  }, [dadosCarregados, carregarTodosLancamentos, filtrarLancamentosPorTipo]);

  // Função para listar extrato (todos os lançamentos)
  const listarExtrato = useCallback(async () => {
    // Se os dados não foram carregados ainda, carrega primeiro
    if (!dadosCarregados) {
      await carregarTodosLancamentos();
    }

    return filtrarLancamentosPorTipo(FILTRO_TIPOS.EXTRATO);
  }, [dadosCarregados, carregarTodosLancamentos, filtrarLancamentosPorTipo]);

  // Função para recarregar dados (força nova consulta)
  const recarregarDados = useCallback(async (filtro?: FiltroLancamentoFinanceiro) => {
    setDadosCarregados(false);
    return await carregarTodosLancamentos(filtro);
  }, [carregarTodosLancamentos]);

  const createReceita = async (data: CreateReceitaData): Promise<LancamentoFinanceiro> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call('/lancamentos-financeiros/receitas', {
        method: 'POST',
        body: JSON.stringify(data),
      });

      if (response.ok) {
        return await response.json();
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao criar receita');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao criar receita';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const findById = async (id: number): Promise<LancamentoFinanceiro> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call(`/lancamentos-financeiros/${id}`);
      if (response.ok) {
        return await response.json();
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao buscar lançamento');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao buscar lançamento';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const updateCostCenterAllocations = async (
    lancamentoId: number,
    allocations: CostCenterAllocation[]
  ): Promise<void> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call(`/lancamentos-financeiros/${lancamentoId}/alocacoes-centro-custo`, {
        method: 'PUT',
        body: JSON.stringify(allocations),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao atualizar alocações');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao atualizar alocações';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getCostCenterAllocations = async (lancamentoId: number): Promise<any[]> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call(`/lancamentos-financeiros/${lancamentoId}/alocacoes-centro-custo`);
      if (response.ok) {
        return await response.json();
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao buscar alocações');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao buscar alocações';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const deleteCostCenterAllocation = async (
    lancamentoId: number,
    allocationId: number
  ): Promise<void> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call(`/lancamentos-financeiros/${lancamentoId}/alocacoes-centro-custo/${allocationId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao excluir alocação');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao excluir alocação';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Função para marcar lançamento como efetivado (pago/recebido)
  const marcarComoEfetivado = async (lancamentoId: number): Promise<void> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call(`/lancamentos-financeiros/${lancamentoId}/efetivar`, {
        method: 'PUT',
      });

      if (response.ok) {
        // Atualiza o lançamento localmente
        setListaLancamentosOriginal(prev =>
          prev.map(l => l.id === lancamentoId ? { ...l, efetivado: true } : l)
        );
        setListaLancamentos(prev =>
          prev.map(l => l.id === lancamentoId ? { ...l, efetivado: true } : l)
        );
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao efetivar lançamento');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao efetivar lançamento';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Função para marcar lançamento como não efetivado
  const marcarComoNaoEfetivado = async (lancamentoId: number): Promise<void> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call(`/lancamentos-financeiros/${lancamentoId}/desefetivar`, {
        method: 'PUT',
      });

      if (response.ok) {
        // Atualiza o lançamento localmente
        setListaLancamentosOriginal(prev =>
          prev.map(l => l.id === lancamentoId ? { ...l, efetivado: false } : l)
        );
        setListaLancamentos(prev =>
          prev.map(l => l.id === lancamentoId ? { ...l, efetivado: false } : l)
        );
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao desefetivar lançamento');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao desefetivar lançamento';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Função para marcar múltiplos lançamentos como efetivados
  const marcarMultiplosComoEfetivados = async (lancamentoIds: number[]): Promise<void> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call('/lancamentos-financeiros/efetivar-multiplos', {
        method: 'PUT',
        body: JSON.stringify({ ids: lancamentoIds }),
      });

      if (response.ok) {
        // Atualiza os lançamentos localmente
        setListaLancamentosOriginal(prev =>
          prev.map(l => lancamentoIds.includes(l.id) ? { ...l, efetivado: true } : l)
        );
        setListaLancamentos(prev =>
          prev.map(l => lancamentoIds.includes(l.id) ? { ...l, efetivado: true } : l)
        );
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao efetivar lançamentos');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao efetivar lançamentos';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return {
    // Estados
    loading,
    error,
    listaLancamentos,
    listaLancamentosOriginal,
    filtroAtivo,
    dadosCarregados,

    // Funções principais (baseadas no legacy)
    carregarTodosLancamentos,
    listarContasApagar,
    listarContasAreceber,
    listarExtrato,
    filtrarLancamentosPorTipo,
    recarregarDados,

    // Funções de efetivação
    marcarComoEfetivado,
    marcarComoNaoEfetivado,
    marcarMultiplosComoEfetivados,

    // Funções originais mantidas para compatibilidade
    createReceita,
    findById,
    updateCostCenterAllocations,
    getCostCenterAllocations,
    deleteCostCenterAllocation,
  };
}
