import { useState, useMemo } from 'react';
import { useAuth } from './useAuth';
import { useApiClient } from '@/utils/apiClient';

export interface CostCenterAllocation {
  centroCustoId: number;
  valor: number;
  porcentagem?: number;
}

export interface CreateReceitaData {
  pessoaId?: number;
  descricao?: string;
  dataLancamento?: string;
  dataCompetencia?: string;
  contaId?: number;
  localId?: number;
  valorBruto?: number;
  valor?: number;
  categoriaLctoFinanceiroId?: number;
  alocacoesCentroCusto?: CostCenterAllocation[];
  repetirReceita?: boolean;
  tipoRepeticao?: string;
  periodicidade?: string;
  quantidadeRepeticoes?: number;
  planoContaCredito?: number;
  fornecedorId?: number;
  observacao?: string;
}

export interface LancamentoFinanceiro {
  id: number;
  descricao: string;
  dataLancamento?: Date;
  dataCompetencia?: Date;
  valorBruto?: number;
  valor: number;
  observacao?: string;
  efetivado?: boolean;
  conciliado?: boolean;
  empresaId: number;
  pessoaId: number;
  pessoaNome?: string;
  contaId?: number;
  contaNome?: string;
  localId?: number;
  localNome?: string;
  fornecedorId?: number;
  fornecedorNome?: string;
  tipoLancamentoFinanceiroId: number;
  categoriaLctoFinanceiroId?: number;
  categoriaNome?: string;
  planoContaCredito?: number;
  planoContaCreditoNome?: string;
  alocacoesCentroCusto?: any[];
  dataHoraUsuarioInc: Date;
  dataHoraUsuarioAlt: Date;
  usuarioInc: string;
  usuarioAlt: string;
}

export function useLancamentosFinanceiros() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { token } = useAuth();

  // Memoize o apiClient para evitar recriações desnecessárias
  const apiClient = useMemo(() => {
    return useApiClient(token);
  }, [token]);

  const createReceita = async (data: CreateReceitaData): Promise<LancamentoFinanceiro> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call('/lancamentos-financeiros/receitas', {
        method: 'POST',
        body: JSON.stringify(data),
      });

      if (response.ok) {
        return await response.json();
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao criar receita');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao criar receita';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const findById = async (id: number): Promise<LancamentoFinanceiro> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call(`/lancamentos-financeiros/${id}`);
      if (response.ok) {
        return await response.json();
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao buscar lançamento');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao buscar lançamento';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const updateCostCenterAllocations = async (
    lancamentoId: number,
    allocations: CostCenterAllocation[]
  ): Promise<void> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call(`/lancamentos-financeiros/${lancamentoId}/alocacoes-centro-custo`, {
        method: 'PUT',
        body: JSON.stringify(allocations),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao atualizar alocações');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao atualizar alocações';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getCostCenterAllocations = async (lancamentoId: number): Promise<any[]> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call(`/lancamentos-financeiros/${lancamentoId}/alocacoes-centro-custo`);
      if (response.ok) {
        return await response.json();
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao buscar alocações');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao buscar alocações';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const deleteCostCenterAllocation = async (
    lancamentoId: number,
    allocationId: number
  ): Promise<void> => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.call(`/lancamentos-financeiros/${lancamentoId}/alocacoes-centro-custo/${allocationId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao excluir alocação');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Erro ao excluir alocação';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    createReceita,
    findById,
    updateCostCenterAllocations,
    getCostCenterAllocations,
    deleteCostCenterAllocation,
  };
}
